{"name": "VisualDebug", "private": true, "type": "module", "description": "可视化平台工程调试工具", "license": "MIT", "scripts": {"dev": "vite --host --port 8080", "build": "node ./node_modules/vite/bin/vite.js build", "build:dev": "vue-tsc && vite build --mode development", "build:test": "vue-tsc && vite build --mode test", "build:pro": "vue-tsc && vite build --mode production", "type:check": "vue-tsc --noEmit --skipLib<PERSON><PERSON><PERSON>", "preview": "pnpm run build:dev && vite preview", "lint:eslint": "eslint --fix --ext .js,.ts,.vue ./src", "lint:prettier": "prettier --write \"src/**/*.{js,ts,json,tsx,css,less,scss,vue,html,md}\"", "lint:stylelint": "stylelint --cache --fix \"**/*.{vue,less,postcss,css,scss}\" --cache --cache-location node_modules/.cache/stylelint/", "lint:lint-staged": "lint-staged", "prepare": "husky install", "release": "standard-version", "commit": "git add -A && czg && git push"}, "dependencies": {"@antv/g2plot": "^2.4.32", "@antv/x6": "^2.18.1", "@antv/x6-plugin-clipboard": "2.1.6", "@antv/x6-plugin-dnd": "2.1.1", "@antv/x6-plugin-export": "^2.1.6", "@antv/x6-plugin-history": "2.2.4", "@antv/x6-plugin-keyboard": "2.2.3", "@antv/x6-plugin-scroller": "2.0.10", "@antv/x6-plugin-selection": "2.2.2", "@antv/x6-plugin-snapline": "2.1.7", "@antv/x6-plugin-transform": "^2.1.8", "@element-plus/icons-vue": "^2.3.1", "@highlightjs/vue-plugin": "^2.1.0", "@iconify/vue": "^4.1.2", "@vueuse/core": "^11.0.3", "axios": "^1.7.7", "crypto-js": "^4.1.1", "dayjs": "^1.11.13", "decimal.js": "^10.5.0", "default-passive-events": "^2.0.0", "echarts": "^5.5.1", "element-plus": "^2.5.6", "entities": "^1.1.2", "highlight.js": "^11.10.0", "markdown-it": "^14.1.0", "md5": "^2.3.0", "mitt": "^3.0.1", "nprogress": "^0.2.0", "pinia": "^2.2.2", "pinia-plugin-persistedstate": "^3.2.1", "print-js": "^1.6.0", "qs": "^6.13.0", "sortablejs": "^1.15.3", "split.js": "^1.6.5", "sprintf-js": "^1.1.3", "v-contextmenu": "^3.2.0", "vue": "^3.5.5", "vue-cropper": "^1.1.1", "vue-i18n": "^9.13.1", "vue-router": "^4.4.5"}, "devDependencies": {"@commitlint/cli": "^19.5.0", "@commitlint/config-conventional": "^19.5.0", "@iconify/json": "^2.2.247", "@types/markdown-it": "^14.1.2", "@types/md5": "^2.3.5", "@types/nprogress": "^0.2.3", "@types/qs": "^6.9.15", "@types/sm-crypto": "^0.3.4", "@types/sortablejs": "^1.15.8", "@types/uuid": "^10.0.0", "@typescript-eslint/eslint-plugin": "^7.14.1", "@typescript-eslint/parser": "^7.14.1", "@vitejs/plugin-vue": "^5.1.3", "@vitejs/plugin-vue-jsx": "^4.0.1", "autoprefixer": "^10.4.20", "code-inspector-plugin": "^0.16.1", "cz-git": "^1.9.4", "czg": "^1.9.4", "eslint": "^8.57.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.1.3", "eslint-plugin-vue": "^9.26.0", "hotkeys-js": "3.13.7", "husky": "^9.0.11", "lint-staged": "^15.2.10", "naive-ui": "^2.39.0", "postcss": "^8.4.45", "postcss-html": "^1.7.0", "prettier": "^3.3.3", "rollup-plugin-visualizer": "^5.14.0", "sass": "1.74.1", "sm-crypto": "^0.3.13", "standard-version": "^9.5.0", "stylelint": "^16.9.0", "stylelint-config-html": "^1.1.0", "stylelint-config-recess-order": "^5.1.0", "stylelint-config-recommended-scss": "^14.1.0", "stylelint-config-recommended-vue": "^1.5.0", "stylelint-config-standard": "^36.0.1", "stylelint-config-standard-scss": "^13.1.0", "typescript": "~5.4.0", "unocss": "^0.62.3", "unplugin-auto-import": "^0.18.3", "unplugin-icons": "^0.19.3", "unplugin-vue-components": "^0.25.2", "unplugin-vue-setup-extend-plus": "^1.0.1", "uuid": "^8.3.2", "vite": "^5.4.5", "vite-plugin-compression": "^0.5.1", "vite-plugin-eslint": "^1.8.1", "vite-plugin-html": "^3.2.2", "vite-plugin-pwa": "^0.20.5", "vite-plugin-svg-icons": "^2.0.1", "vite-plugin-vue-devtools": "^7.3.5", "vue-tsc": "^2.1.6"}, "overrides": {}, "engines": {"node": ">=16.0.0"}, "browserslist": {"production": ["> 1%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "config": {"commitizen": {"path": "node_modules/cz-git"}}}